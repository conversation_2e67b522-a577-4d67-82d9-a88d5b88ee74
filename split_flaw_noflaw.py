#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import shutil
import sys
from pathlib import Path

LINE_RE = re.compile(r'^(?P<name>[^:]+):\s*\[(?P<classes>[^\]]*)\]\s*$')

def parse_label_file(txt_path: Path):
    mapping = {}
    with txt_path.open('r', encoding='utf-8', errors='ignore') as f:
        for raw in f:
            line = raw.strip()
            m = LINE_RE.match(line)
            if not m:
                continue
            name = m.group('name').strip()
            classes_str = m.group('classes').strip()
            cls_set = set()
            if classes_str:
                for tok in classes_str.split(','):
                    tok = tok.strip()
                    if tok.lstrip('-').isdigit():
                        cls_set.add(int(tok))
            mapping[name] = cls_set
    return mapping

def decide_bucket(cls_set):
    if cls_set == {0}:
        return "noflaw"
    if 1 in cls_set or len(cls_set) >= 2:
        return "flaw"
    return "noflaw"

def safe_copy(src: Path, dst_dir: Path):
    if src.is_file():
        shutil.copy2(src, dst_dir / src.name)
    else:
        print(f"[WARN] fichier manquant: {src}")

def main():
    """
    Fonction principale pour l'exécution via l'interface graphique
    L'argument images_dir est passé via sys.argv
    """

    if len(sys.argv) != 2:
        print("Usage: python split_flaw_noflaw.py <images_dir>")
        return

    images_dir_path = sys.argv[1]
    images_dir = Path(images_dir_path)

    print(f"📁 Dossier d'images: {images_dir}")

    # Vérifications
    if not images_dir.exists():
        print(f"❌ Erreur: Le dossier d'images n'existe pas: {images_dir}")
        return

    # Déterminer les chemins basés sur images_dir
    masks_dir = images_dir / "masks_binary"
    if not masks_dir.exists():
        print(f"❌ Erreur: Le dossier masks_binary n'existe pas: {masks_dir}")
        return

    # Chercher le fichier de labels dans masks_binary
    txt_files = list(masks_dir.glob("view_labels_results*.txt"))
    if not txt_files:
        print(f"❌ Erreur: Aucun fichier view_labels_results*.txt trouvé dans: {masks_dir}")
        return

    # Prendre le fichier le plus récent
    txt_path = max(txt_files, key=lambda p: p.stat().st_mtime)
    print(f"📄 Fichier de labels: {txt_path}")

    # Dossier de sortie au même niveau que le dossier d'images
    out_root = images_dir.parent / "nnunet_split"
    print(f"📂 Dossier de sortie: {out_root}")

    # === Dossiers de sortie ===
    # Dossiers pour les images
    images_flaw = out_root / "flaw"
    images_noflaw = out_root / "noflaw"

    # Dossier gtmask contenant les sous-dossiers pour les labels
    gtmask_root = out_root / "gtmask"
    labels_flaw = gtmask_root / "flaw"
    labels_noflaw = gtmask_root / "noflaw"

    for d in [images_flaw, images_noflaw, labels_flaw, labels_noflaw]:
        d.mkdir(parents=True, exist_ok=True)
        print(f"📁 Créé: {d}")

    print("\n🔍 Analyse du fichier de labels...")
    mapping = parse_label_file(txt_path)
    stats = {"flaw_images": 0, "noflaw_images": 0, "flaw_masks": 0, "noflaw_masks": 0}

    total_files = len(mapping)
    print(f"📊 {total_files} fichiers à traiter")

    processed = 0
    for fname, cls_set in mapping.items():
        bucket = decide_bucket(cls_set)

        mask_path = masks_dir / fname
        img_path = images_dir / fname

        if bucket == "flaw":
            safe_copy(mask_path, labels_flaw)
            safe_copy(img_path, images_flaw)
            stats["flaw_masks"] += 1
            stats["flaw_images"] += 1
        else:
            safe_copy(mask_path, labels_noflaw)
            safe_copy(img_path, images_noflaw)
            stats["noflaw_masks"] += 1
            stats["noflaw_images"] += 1

        processed += 1
        if processed % 100 == 0:
            print(f"⏳ Traité: {processed}/{total_files}")

    print("\n=== Résumé ===")
    for k, v in stats.items():
        print(f"{k}: {v}")

    print(f"\n✅ Traitement terminé! Résultats dans: {out_root}")

if __name__ == "__main__":
    main()
