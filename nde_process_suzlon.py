#!/usr/bin/env python3
"""
Script pour traiter automatiquement tous les fichiers NDE Suzlon d'un dossier
et les convertir en images PNG en utilisant le script nde_NdeSuzlonToPng.py
"""

import os
import re
import unicodedata
import argparse
import sys
from nde_NdeSuzlonToPng import process_suzlon_nde_file

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Traite automatiquement tous les fichiers NDE Suzlon d\'un dossier')
    parser.add_argument('--source', type=str, default="./ndes",
                       help='Dossier source contenant les fichiers NDE Suzlon')
    parser.add_argument('--destination', type=str, default="./images_suzlon",
                       help='Dossier destination pour les images générées')
    parser.add_argument('--auto-confirm', action='store_true',
                       help='Confirme automatiquement le traitement (pour interface graphique)')
    parser.add_argument('--preserve-parent-folders', action='store_true',
                       help='Préserve la structure des dossiers parents (ex: "From ...")')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Affichage détaillé pour chaque fichier')
    parser.add_argument('--colormap', default="./OmniScanColorMap.npy",
                       help='Chemin vers le fichier colormap OmniScan (défaut: ./OmniScanColorMap.npy)')
    parser.add_argument('--no-transpose', action='store_true',
                       help='Désactiver la transposition des données (activée par défaut)')
    parser.add_argument('--no-rotation', action='store_true',
                       help='Désactiver la rotation 90° anti-horaire (activée par défaut)')
    return parser.parse_args()

# Parse arguments
args = parse_arguments()
SOURCE_FOLDER = args.source
OUTPUT_BASE_FOLDER = args.destination
AUTO_CONFIRM = args.auto_confirm
PRESERVE_PARENT_FOLDERS = args.preserve_parent_folders
VERBOSE = args.verbose
COLORMAP_PATH = args.colormap
APPLY_TRANSPOSE = not args.no_transpose
APPLY_ROTATION = not args.no_rotation

print(f"[INFO] Dossier source: {SOURCE_FOLDER}")
print(f"[INFO] Dossier destination: {OUTPUT_BASE_FOLDER}")
print(f"[INFO] Préservation des dossiers parents: {'Oui' if PRESERVE_PARENT_FOLDERS else 'Non'}")
print(f"[INFO] Mode verbose: {'Oui' if VERBOSE else 'Non'}")
print(f"[INFO] Transposition: {'Oui' if APPLY_TRANSPOSE else 'Non'}")
print(f"[INFO] Rotation 90°: {'Oui' if APPLY_ROTATION else 'Non'}")
print(f"[INFO] Colormap: {COLORMAP_PATH}")

# Crée le dossier de base s'il n'existe pas
os.makedirs(OUTPUT_BASE_FOLDER, exist_ok=True)

# Normalisation de noms
def normalize_name(name):
    name = os.path.splitext(name)[0]
    name = unicodedata.normalize('NFD', name).encode('ascii', 'ignore').decode('utf-8')
    name = re.sub(r'\W+', '', name)
    return name.lower().strip()

# Fonction pour trouver récursivement tous les fichiers .nde
def find_nde_files(directory):
    nde_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('.nde'):
                full_path = os.path.join(root, file)
                relative_path = os.path.relpath(full_path, SOURCE_FOLDER)
                nde_files.append((full_path, relative_path))
    return nde_files

# Liste des fichiers .nde (maintenant avec leurs chemins complets)
nde_files = find_nde_files(SOURCE_FOLDER)

# Fonction pour vérifier si un dossier contient déjà des images Suzlon
def has_suzlon_images(directory):
    """Vérifie si le dossier contient déjà des images générées par le script Suzlon"""
    if not os.path.exists(directory):
        return False
    
    # Chercher les dossiers caractéristiques du script Suzlon
    endviews_uint8_dir = os.path.join(directory, 'endviews_uint8', 'complete')
    endviews_rgb24_dir = os.path.join(directory, 'endviews_rgb24', 'complete')
    
    # Vérifier si les dossiers existent et contiennent des fichiers PNG
    for check_dir in [endviews_uint8_dir, endviews_rgb24_dir]:
        if os.path.exists(check_dir):
            png_files = [f for f in os.listdir(check_dir) if f.lower().endswith('.png')]
            if png_files:
                return True
    
    return False

# Fonction pour trouver récursivement tous les dossiers de destination existants
def find_existing_output_dirs(base_folder):
    """Trouve récursivement tous les dossiers dans le dossier de destination"""
    existing_dirs_normalized = {}
    if not os.path.exists(base_folder):
        return existing_dirs_normalized

    for root, dirs, files in os.walk(base_folder):
        for dir_name in dirs:
            full_path = os.path.join(root, dir_name)
            normalized = normalize_name(dir_name)
            # Stocker le chemin complet pour chaque nom normalisé
            if normalized not in existing_dirs_normalized:
                existing_dirs_normalized[normalized] = []
            existing_dirs_normalized[normalized].append(full_path)

    return existing_dirs_normalized

# Liste des dossiers déjà présents dans OUTPUT_BASE_FOLDER (récursivement)
existing_dirs_normalized = find_existing_output_dirs(OUTPUT_BASE_FOLDER)

# Vérification
files_to_process = []
files_skipped = []

print("\n[INFO] Verification des correspondances entre fichiers .nde et dossiers de sortie...\n")

for nde_path, relative_path in nde_files:
    original_name = os.path.splitext(os.path.basename(relative_path))[0]
    normalized = normalize_name(original_name)

    # Construire le chemin de sortie en préservant ou non la structure des dossiers
    if PRESERVE_PARENT_FOLDERS:
        # Préserver la structure des dossiers parents
        relative_dir = os.path.dirname(relative_path)
        if relative_dir:
            output_subfolder = os.path.join(OUTPUT_BASE_FOLDER, relative_dir, original_name)
        else:
            output_subfolder = os.path.join(OUTPUT_BASE_FOLDER, original_name)
    else:
        # Structure plate (comportement original)
        output_subfolder = os.path.join(OUTPUT_BASE_FOLDER, original_name)

    print(f"[TEST] {relative_path} (-> {output_subfolder})")

    # Vérifier si le dossier existe déjà et contient des images Suzlon
    if has_suzlon_images(output_subfolder):
        print(f"[OK] Images Suzlon déjà présentes dans : {output_subfolder}")
        files_skipped.append(relative_path)
    elif normalized in existing_dirs_normalized:
        # Vérifier si un des dossiers existants contient des images Suzlon
        existing_paths = existing_dirs_normalized[normalized]
        found_existing = None

        for existing_path in existing_paths:
            if has_suzlon_images(existing_path):
                found_existing = existing_path
                break

        if found_existing:
            print(f"[OK] Images Suzlon déjà présentes dans : {found_existing}")
            files_skipped.append(relative_path)
        else:
            print(f"[NOUVEAU] Dossier existe mais pas d'images Suzlon -> à traiter")
            files_to_process.append((nde_path, output_subfolder))
    else:
        print(f"[NOUVEAU] Aucune correspondance -> à traiter")
        files_to_process.append((nde_path, output_subfolder))

# Résumé
print("\n==== RÉSUMÉ ====")
print(f"Total fichiers .nde : {len(nde_files)}")
print(f"Déjà traités        : {len(files_skipped)}")
print(f"A traiter maintenant : {len(files_to_process)}")

if not files_to_process:
    print("\n[OK] Aucun nouveau fichier à traiter.")
    sys.exit(0)

# Confirmation
if AUTO_CONFIRM:
    print("\n[AUTO] Traitement automatique lancé depuis l'interface graphique")
    proceed = 'o'
else:
    proceed = input("\n[ATTENTION] Voulez-vous lancer le traitement ? (o/n) : ").strip().lower()

if proceed != 'o':
    print("\n[ANNULE] Traitement annulé.")
    sys.exit(0)

# Traitement
success_count = 0
error_count = 0

for nde_path, output_subfolder in files_to_process:
    print(f"\n[TRAITEMENT] Traitement : {os.path.basename(nde_path)}")
    print(f"[DOSSIER] Dossier de sortie dédié : {output_subfolder}")
    
    # Créer le dossier de sortie
    os.makedirs(output_subfolder, exist_ok=True)

    try:
        # Traiter le fichier avec le script Suzlon
        success = process_suzlon_nde_file(
            nde_file=nde_path,
            output_dir=output_subfolder,
            verbose=VERBOSE,
            path_omniscancolormap=COLORMAP_PATH,
            apply_transpose=APPLY_TRANSPOSE,
            apply_rotation=APPLY_ROTATION
        )
        
        if success:
            print(f"[OK] Terminé : {os.path.basename(nde_path)}")
            success_count += 1
        else:
            print(f"[ERREUR] Échec du traitement : {os.path.basename(nde_path)}")
            error_count += 1
            
    except Exception as e:
        print(f"[ERREUR] Erreur avec {os.path.basename(nde_path)} : {e}")
        error_count += 1

print(f"\n==== RÉSULTAT FINAL ====")
print(f"Fichiers traités avec succès : {success_count}")
print(f"Fichiers en erreur : {error_count}")
print(f"Total traité : {success_count + error_count}")

if error_count == 0:
    print("\n[OK] Tous les fichiers sélectionnés ont été traités avec succès.")
else:
    print(f"\n[ATTENTION] {error_count} fichier(s) ont échoué lors du traitement.")
